{"compilerOptions": {"strict": true, "target": "ES2021", "module": "ESNext", "moduleResolution": "bundler", "jsx": "react-native", "allowJs": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "types": ["expo"], "paths": {"@/*": ["./*"]}, "baseUrl": "."}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts"], "extends": "expo/tsconfig.base"}