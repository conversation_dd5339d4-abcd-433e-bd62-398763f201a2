// app/(tabs)/_layout.tsx
import { Tabs } from 'expo-router';
import { Platform } from 'react-native';
import { useColorScheme } from 'nativewind';
import { Colors } from '@/constants/Colors';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const scheme = colorScheme ?? 'light';

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: Colors[scheme].tint,
        tabBarInactiveTintColor: '#aaa',
        tabBarStyle: {
          backgroundColor: Colors[scheme].background,
          borderTopWidth: 0,
          height: 64,
          ...Platform.select({
            ios: {
              paddingBottom: 20,
              position: 'absolute',
            },
          }),
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        tabBarItemStyle: {
          paddingVertical: 6,
        },
      }}
    />
  );
}
