
import { differenceInWeeks } from 'date-fns';

const growthData = [
  { week: 'Birth', weight: 7.2, height: 20 },
  { week: '2W', weight: 7.8, height: 20.5 },
  { week: '4W', weight: 8.5, height: 21 },
  { week: '6W', weight: 9.2, height: 21.5 },
  { week: '8W', weight: 10.1, height: 22 },
];

const chartConfig = {
  weight: {
    label: "Weight (lbs)",
    color: "hsl(var(--chart-1))",
  },
  height: {
    label: "Height (inches)", 
    color: "hsl(var(--chart-2))",
  },
};

const getNewbornInsights = (weeks: number, t: (key: string) => string): string[] => {
  const allInsightKeys: { [key: string]: string[] } = {
    '0-1': [
      'newborn.insights.weeks.0-1.1',
      'newborn.insights.weeks.0-1.2',
      'newborn.insights.weeks.0-1.3'
    ],
    '2-4': [
      'newborn.insights.weeks.2-4.1',
      'newborn.insights.weeks.2-4.2',
      'newborn.insights.weeks.2-4.3'
    ],
    '5-8': [
      'newborn.insights.weeks.5-8.1',
      'newborn.insights.weeks.5-8.2',
      'newborn.insights.weeks.5-8.3'
    ],
    '9-12': [
      'newborn.insights.weeks.9-12.1',
      'newborn.insights.weeks.9-12.2',
      'newborn.insights.weeks.9-12.3'
    ],
  };

  let insightKeys: string[];
  if (weeks <= 1) insightKeys = allInsightKeys['0-1'];
  else if (weeks <= 4) insightKeys = allInsightKeys['2-4'];
  else if (weeks <= 8) insightKeys = allInsightKeys['5-8'];
  else if (weeks <= 12) insightKeys = allInsightKeys['9-12'];
  else {
    insightKeys = [
      'newborn.insights.default.1',
      'newborn.insights.default.2',
      'newborn.insights.default.3'
    ];
  }
  return insightKeys.map(key => t(key));
};

export const useNewbornData = (selectedDate: Date | undefined, t: (key: string) => string) => {
  const currentDate = selectedDate || new Date();
  
  // For demonstration, let's assume a birth date. In a real app, this would be from user data.
  const birthDate = new Date('2025-04-20');
  const ageInWeeks = differenceInWeeks(currentDate, birthDate);

  const currentGrowthData = 
    [...growthData].reverse().find(d => {
      const weekNumber = d.week === 'Birth' ? 0 : parseInt(d.week, 10);
      return !isNaN(weekNumber) && weekNumber <= ageInWeeks;
    }) || growthData[0];

  const aiInsights = getNewbornInsights(ageInWeeks, t);

  return {
    currentDate,
    ageInWeeks,
    currentGrowthData,
    aiInsights,
    growthData,
    chartConfig
  };
};
