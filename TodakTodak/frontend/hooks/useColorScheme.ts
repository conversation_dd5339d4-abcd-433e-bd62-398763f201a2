import { useColorScheme as useDeviceColorScheme } from 'react-native';
import { useEffect, useState } from 'react';

// 선택 가능한 테마 타입
export type ThemeMode = 'light' | 'dark' | 'system';

export function useColorScheme() {
  const deviceScheme = useDeviceColorScheme(); // 시스템 다크/라이트 감지
  const [themeMode, setThemeMode] = useState<ThemeMode>('system');

  const colorScheme = themeMode === 'system' ? deviceScheme : themeMode;

  const toggleColorScheme = () => {
    setThemeMode((prev) => {
      if (prev === 'light') return 'dark';
      if (prev === 'dark') return 'light';
      return deviceScheme === 'dark' ? 'light' : 'dark';
    });
  };

  const setColorScheme = (scheme: ThemeMode) => {
    setThemeMode(scheme);
  };

  // 선택적: 앱 시작 시 저장된 테마 불러오기 (AsyncStorage 활용 시 확장 가능)

  return {
    colorScheme,         // 실제 적용할 테마 (light/dark)
    themeMode,           // 사용자가 선택한 설정값 (light/dark/system)
    setColorScheme,      // 직접 설정
    toggleColorScheme,   // 라이트/다크 토글
  };
}
