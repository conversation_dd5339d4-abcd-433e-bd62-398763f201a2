const { fontFamily } = require('tailwindcss/defaultTheme');

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    './App.{js,jsx,ts,tsx}',
    './app/**/*.{js,jsx,ts,tsx}',
    './components/**/*.{js,jsx,ts,tsx}',
    './src/**/*.{js,jsx,ts,tsx}',
  ],
  presets: [require('nativewind/preset')],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        background: '#fef9fa',
        foreground: '#572b2e',

        card: '#fdf6f7',
        'card-foreground': '#572b2e',

        popover: '#fdf6f7',
        'popover-foreground': '#572b2e',

        primary: '#e96984',
        'primary-foreground': '#ffffff',

        secondary: '#f8e6eb',
        'secondary-foreground': '#7d3d45',

        muted: '#fbf2f4',
        'muted-foreground': '#a76a7f',

        accent: '#f3e4e7',
        'accent-foreground': '#6b3740',

        destructive: '#e5484d',
        'destructive-foreground': '#fafafa',

        border: '#eee3e6',
        input: '#eee3e6',
        ring: '#e96984',

        // 로고에서 영감받은 색상
        rose: {
          50: 'hsl(350 20% 98%)',
          100: 'hsl(350 15% 95%)',
          200: 'hsl(348 20% 88%)',
          300: 'hsl(346 25% 80%)',
          400: 'hsl(344 60% 75%)',
          500: 'hsl(342 65% 70%)',
          600: 'hsl(340 70% 65%)',
          700: 'hsl(338 65% 55%)',
          800: 'hsl(336 60% 45%)',
          900: 'hsl(334 55% 35%)',
        },
        blush: {
          50: 'hsl(355 25% 98%)',
          100: 'hsl(355 20% 95%)',
          200: 'hsl(353 25% 88%)',
          300: 'hsl(351 30% 80%)',
          400: 'hsl(349 65% 75%)',
          500: 'hsl(347 70% 70%)',
          600: 'hsl(345 75% 65%)',
          700: 'hsl(343 70% 55%)',
          800: 'hsl(341 65% 45%)',
          900: 'hsl(339 60% 35%)',
        },
      },
      fontFamily: {
        sans: ['"Pretendard Variable"', ...fontFamily.sans],
      },
      fontSize: {
        display: ['32px', { lineHeight: '40px', letterSpacing: '-0.5px' }],
        heading: ['24px', { lineHeight: '32px', fontWeight: '600' }],
        body: ['16px', { lineHeight: '24px' }],
        small: ['14px', { lineHeight: '20px' }],
        label: ['12px', { lineHeight: '16px', fontWeight: '500' }],
      },
      spacing: {
        section: '24px',
        card: '16px',
        tight: '8px',
        loose: '32px',
      },
      boxShadow: {
        card: '0px 1px 4px rgba(0,0,0,0.06)',
        header: '0px 2px 6px rgba(0,0,0,0.08)',
        modal: '0px 4px 12px rgba(0,0,0,0.12)',
      },
      borderRadius: {
        lg: '12px',
        xl: '16px',
        full: '9999px',
      },
      keyframes: {
        'logo-spin': {
          from: { transform: 'rotate(0deg)' },
          to: { transform: 'rotate(360deg)' },
        },
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'logo-spin': 'logo-spin 20s linear infinite',
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};
